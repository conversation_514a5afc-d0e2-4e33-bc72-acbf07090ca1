/* تنسيق إضافة Fast US VPN */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    direction: rtl;
    text-align: right;
}

.container {
    width: 350px;
    min-height: 500px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e0e0e0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-img {
    width: 32px;
    height: 32px;
    border-radius: 8px;
}

.logo h1 {
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #e74c3c;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: #27ae60;
}

.status-text {
    font-size: 12px;
    font-weight: 500;
    color: #7f8c8d;
}

/* Main Toggle Button */
.main-toggle {
    text-align: center;
    margin: 30px 0;
}

.toggle-btn {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: none;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
}

.toggle-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 35px rgba(231, 76, 60, 0.6);
}

.toggle-btn.active {
    background: linear-gradient(135deg, #27ae60, #229954);
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
}

.toggle-btn.active:hover {
    box-shadow: 0 12px 35px rgba(39, 174, 96, 0.6);
}

.toggle-icon {
    font-size: 32px;
}

.toggle-text {
    font-size: 14px;
    font-weight: bold;
}

/* Connection Info */
.connection-info {
    background: rgba(52, 152, 219, 0.1);
    border-radius: 10px;
    padding: 15px;
    margin: 20px 0;
    border-right: 4px solid #3498db;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 8px 0;
}

.info-label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 13px;
}

.info-value {
    font-weight: 500;
    color: #3498db;
    font-size: 13px;
}

/* Server Section */
.server-section {
    margin: 20px 0;
}

.server-section h3 {
    font-size: 16px;
    color: #2c3e50;
    margin-bottom: 15px;
    text-align: center;
}

.server-list {
    max-height: 120px;
    overflow-y: auto;
    border-radius: 8px;
    background: #f8f9fa;
    padding: 10px;
}

.server-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin: 5px 0;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e0e0e0;
}

.server-item:hover {
    background: #e3f2fd;
    border-color: #2196f3;
}

.server-item.active {
    background: #e8f5e8;
    border-color: #4caf50;
}

.server-name {
    font-size: 12px;
    font-weight: 500;
    color: #2c3e50;
}

.server-speed {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    color: white;
}

.speed-fast { background: #27ae60; }
.speed-medium { background: #f39c12; }
.speed-slow { background: #e74c3c; }

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 10px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.action-btn {
    flex: 1;
    min-width: 100px;
    padding: 10px 8px;
    border: none;
    border-radius: 8px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

.btn-icon {
    font-size: 14px;
}

/* Loading */
.loading {
    text-align: center;
    padding: 20px;
    color: #7f8c8d;
}

.spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #e0e0e0;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e0e0e0;
}

.footer-text {
    display: flex;
    justify-content: space-around;
    font-size: 11px;
    color: #7f8c8d;
    margin-bottom: 8px;
}

.version {
    font-size: 10px;
    color: #bdc3c7;
}

/* Animations */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Scrollbar */
.server-list::-webkit-scrollbar {
    width: 6px;
}

.server-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.server-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.server-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
