// قائمة البروكسيات الأمريكية المجانية المحدثة
const US_PROXIES = [
  {
    host: "************",
    port: 8080,
    type: "http",
    location: "Oregon, US",
    speed: "fast",
    anonymity: "high"
  },
  {
    host: "**************",
    port: 1080,
    type: "socks5",
    location: "Atlanta, US",
    speed: "fast",
    anonymity: "high"
  },
  {
    host: "**************",
    port: 8080,
    type: "http",
    location: "New Jersey, US",
    speed: "medium",
    anonymity: "high"
  },
  {
    host: "*************",
    port: 443,
    type: "https",
    location: "North Carolina, US",
    speed: "fast",
    anonymity: "medium"
  },
  {
    host: "*************",
    port: 8080,
    type: "http",
    location: "New Jersey, US",
    speed: "medium",
    anonymity: "high"
  },
  {
    host: "**************",
    port: 8080,
    type: "http",
    location: "California, US",
    speed: "medium",
    anonymity: "medium"
  },
  {
    host: "**************",
    port: 1080,
    type: "socks5",
    location: "New Jersey, US",
    speed: "medium",
    anonymity: "high"
  },
  {
    host: "***************",
    port: 8080,
    type: "http",
    location: "Texas, US",
    speed: "medium",
    anonymity: "high"
  },
  {
    host: "*************",
    port: 8080,
    type: "http",
    location: "Texas, US",
    speed: "medium",
    anonymity: "medium"
  },
  {
    host: "*************",
    port: 8080,
    type: "http",
    location: "Arizona, US",
    speed: "medium",
    anonymity: "high"
  }
];

// دالة للحصول على بروكسي عشوائي
function getRandomProxy() {
  const randomIndex = Math.floor(Math.random() * US_PROXIES.length);
  return US_PROXIES[randomIndex];
}

// دالة للحصول على أفضل البروكسيات (الأسرع)
function getFastProxies() {
  return US_PROXIES.filter(proxy => proxy.speed === "fast");
}

// دالة لاختبار البروكسي
async function testProxy(proxy) {
  return new Promise((resolve) => {
    const startTime = Date.now();
    const timeout = setTimeout(() => {
      resolve({ success: false, latency: 9999 });
    }, 5000);

    // محاولة الاتصال بالبروكسي
    fetch('https://httpbin.org/ip', {
      method: 'GET',
      signal: AbortSignal.timeout(5000)
    }).then(response => {
      clearTimeout(timeout);
      const latency = Date.now() - startTime;
      resolve({ 
        success: response.ok, 
        latency: latency,
        status: response.status 
      });
    }).catch(() => {
      clearTimeout(timeout);
      resolve({ success: false, latency: 9999 });
    });
  });
}

// تصدير الدوال والمتغيرات
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { US_PROXIES, getRandomProxy, getFastProxies, testProxy };
}
