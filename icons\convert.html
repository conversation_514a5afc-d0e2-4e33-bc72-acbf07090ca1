<!DOCTYPE html>
<html>
<head>
    <title>SVG to PNG Converter</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-container { margin: 20px; display: inline-block; text-align: center; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        button { padding: 10px 20px; margin: 5px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #2980b9; }
    </style>
</head>
<body>
    <h1>تحويل أيقونات SVG إلى PNG</h1>
    
    <div class="icon-container">
        <h3>الأيقونة العادية</h3>
        <canvas id="canvas16" width="16" height="16"></canvas>
        <canvas id="canvas32" width="32" height="32"></canvas>
        <canvas id="canvas48" width="48" height="48"></canvas>
        <canvas id="canvas128" width="128" height="128"></canvas>
        <br>
        <button onclick="convertIcon('icon.svg', false)">تحويل الأيقونة العادية</button>
    </div>
    
    <div class="icon-container">
        <h3>الأيقونة النشطة</h3>
        <canvas id="canvasActive16" width="16" height="16"></canvas>
        <canvas id="canvasActive32" width="32" height="32"></canvas>
        <canvas id="canvasActive48" width="48" height="48"></canvas>
        <canvas id="canvasActive128" width="128" height="128"></canvas>
        <br>
        <button onclick="convertIcon('icon-active.svg', true)">تحويل الأيقونة النشطة</button>
    </div>

    <script>
        async function convertIcon(svgFile, isActive) {
            try {
                const response = await fetch(svgFile);
                const svgText = await response.text();
                
                const sizes = [16, 32, 48, 128];
                
                for (const size of sizes) {
                    const canvasId = isActive ? `canvasActive${size}` : `canvas${size}`;
                    const canvas = document.getElementById(canvasId);
                    const ctx = canvas.getContext('2d');
                    
                    // إنشاء صورة من SVG
                    const img = new Image();
                    const svgBlob = new Blob([svgText], { type: 'image/svg+xml' });
                    const url = URL.createObjectURL(svgBlob);
                    
                    img.onload = function() {
                        // مسح الكانفاس
                        ctx.clearRect(0, 0, size, size);
                        
                        // رسم الصورة
                        ctx.drawImage(img, 0, 0, size, size);
                        
                        // تحويل إلى PNG وتحميل
                        canvas.toBlob(function(blob) {
                            const link = document.createElement('a');
                            link.download = isActive ? `icon${size}-active.png` : `icon${size}.png`;
                            link.href = URL.createObjectURL(blob);
                            link.click();
                        });
                        
                        URL.revokeObjectURL(url);
                    };
                    
                    img.src = url;
                }
                
                alert('تم تحويل الأيقونات بنجاح!');
            } catch (error) {
                console.error('خطأ في التحويل:', error);
                alert('حدث خطأ في التحويل');
            }
        }
        
        // تحويل تلقائي عند تحميل الصفحة
        window.onload = function() {
            // يمكن إلغاء التعليق لتحويل تلقائي
            // setTimeout(() => convertIcon('icon.svg', false), 1000);
            // setTimeout(() => convertIcon('icon-active.svg', true), 2000);
        };
    </script>
</body>
</html>
