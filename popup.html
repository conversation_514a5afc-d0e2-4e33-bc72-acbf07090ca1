<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <style>
    body {
      width: 300px;
      padding: 10px;
      font-family: Arial, sans-serif;
    }
    .container {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    select {
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #ccc;
    }
    button {
      padding: 10px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #45a049;
    }
    button.disconnect {
      background-color: #f44336;
    }
    button.disconnect:hover {
      background-color: #da190b;
    }
    .status {
      padding: 10px;
      border-radius: 4px;
      text-align: center;
      margin-top: 10px;
    }
    .connected {
      background-color: #dff0d8;
      color: #3c763d;
    }
    .disconnected {
      background-color: #f2dede;
      color: #a94442;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>US Fast Proxy VPN</h2>
    <select id="proxyList">
      <option value="">اختر سيرفر بروكسي</option>
    </select>
    <button id="connect">اتصال</button>
    <button id="disconnect" class="disconnect">قطع الاتصال</button>
    <div id="status" class="status disconnected">
      غير متصل
    </div>
    <hr>
    <h3>إضافة بروكسي جديد</h3>
    <input type="text" id="proxyName" placeholder="اسم البروكسي (مثال: US Server 1)">
    <input type="text" id="proxyHost" placeholder="عنوان البروكسي (IP أو Hostname)">
    <input type="number" id="proxyPort" placeholder="رقم المنفذ (Port)">
    <button id="addProxy">إضافة بروكسي</button>
    <button id="removeProxy" class="disconnect">إزالة البروكسي المحدد</button>
  </div>
  <script src="popup.js"></script>
</body>
</html> 