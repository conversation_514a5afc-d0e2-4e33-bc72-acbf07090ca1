// خدمة الخلفية لإدارة البروكسي
importScripts('proxy-list.js');

let isVpnEnabled = false;
let currentProxy = null;
let proxyTestResults = new Map();

// إعداد البروكسي
async function setProxy(proxy) {
  if (!proxy) return false;

  try {
    const config = {
      mode: "fixed_servers",
      rules: {
        singleProxy: {
          scheme: proxy.type === 'socks5' ? 'socks5' : 'http',
          host: proxy.host,
          port: proxy.port
        },
        bypassList: ["localhost", "127.0.0.1", "::1"]
      }
    };

    await chrome.proxy.settings.set({
      value: config,
      scope: 'regular'
    });

    currentProxy = proxy;
    await chrome.storage.local.set({
      currentProxy: proxy,
      isVpnEnabled: true
    });

    // تحديث أيقونة الإضافة
    await updateIcon(true);

    return true;
  } catch (error) {
    console.error('خطأ في إعداد البروكسي:', error);
    return false;
  }
}

// إلغاء البروكسي
async function clearProxy() {
  try {
    await chrome.proxy.settings.clear({ scope: 'regular' });
    currentProxy = null;
    isVpnEnabled = false;

    await chrome.storage.local.set({
      currentProxy: null,
      isVpnEnabled: false
    });

    // تحديث أيقونة الإضافة
    await updateIcon(false);

    return true;
  } catch (error) {
    console.error('خطأ في إلغاء البروكسي:', error);
    return false;
  }
}

// تحديث أيقونة الإضافة
async function updateIcon(enabled) {
  const iconPath = enabled ? {
    "16": "icons/icon16-active.png",
    "32": "icons/icon32-active.png",
    "48": "icons/icon48-active.png",
    "128": "icons/icon128-active.png"
  } : {
    "16": "icons/icon16.png",
    "32": "icons/icon32.png",
    "48": "icons/icon48.png",
    "128": "icons/icon128.png"
  };

  try {
    await chrome.action.setIcon({ path: iconPath });
    await chrome.action.setBadgeText({
      text: enabled ? "ON" : ""
    });
    await chrome.action.setBadgeBackgroundColor({
      color: enabled ? "#4CAF50" : "#FF5722"
    });
  } catch (error) {
    console.error('خطأ في تحديث الأيقونة:', error);
  }
}

// اختبار البروكسيات وترتيبها حسب السرعة
async function testAllProxies() {
  console.log('بدء اختبار البروكسيات...');

  for (const proxy of US_PROXIES) {
    const result = await testProxy(proxy);
    proxyTestResults.set(`${proxy.host}:${proxy.port}`, result);

    // حفظ النتائج في التخزين المحلي
    const results = Object.fromEntries(proxyTestResults);
    await chrome.storage.local.set({ proxyTestResults: results });
  }

  console.log('انتهى اختبار البروكسيات');
}

// الحصول على أفضل بروكسي متاح
function getBestProxy() {
  let bestProxy = null;
  let bestLatency = Infinity;

  for (const proxy of US_PROXIES) {
    const key = `${proxy.host}:${proxy.port}`;
    const result = proxyTestResults.get(key);

    if (result && result.success && result.latency < bestLatency) {
      bestLatency = result.latency;
      bestProxy = proxy;
    }
  }

  return bestProxy || getRandomProxy();
}

// معالج الرسائل من popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  switch (request.action) {
    case 'toggleVpn':
      handleToggleVpn().then(sendResponse);
      return true;

    case 'getStatus':
      sendResponse({
        isEnabled: isVpnEnabled,
        currentProxy: currentProxy,
        testResults: Object.fromEntries(proxyTestResults)
      });
      break;

    case 'testProxies':
      testAllProxies().then(() => {
        sendResponse({ success: true });
      });
      return true;

    case 'switchProxy':
      switchToNextProxy().then(sendResponse);
      return true;

    case 'setSpecificProxy':
      setSpecificProxy(request.proxy).then(sendResponse);
      return true;
  }
});

// تبديل حالة VPN
async function handleToggleVpn() {
  if (isVpnEnabled) {
    const success = await clearProxy();
    return { success, enabled: false };
  } else {
    const proxy = getBestProxy();
    const success = await setProxy(proxy);
    return { success, enabled: success, proxy };
  }
}

// التبديل إلى البروكسي التالي
async function switchToNextProxy() {
  if (!isVpnEnabled) return { success: false };

  const proxy = getBestProxy();
  const success = await setProxy(proxy);
  return { success, proxy };
}

// إعداد بروكسي محدد
async function setSpecificProxy(proxy) {
  if (!proxy) return { success: false };

  const success = await setProxy(proxy);
  return { success, proxy };
}

// تهيئة الإضافة عند بدء التشغيل
chrome.runtime.onStartup.addListener(async () => {
  // استرجاع الحالة المحفوظة
  const data = await chrome.storage.local.get(['isVpnEnabled', 'currentProxy', 'proxyTestResults']);

  if (data.isVpnEnabled && data.currentProxy) {
    isVpnEnabled = true;
    currentProxy = data.currentProxy;
    await setProxy(currentProxy);
  }

  if (data.proxyTestResults) {
    proxyTestResults = new Map(Object.entries(data.proxyTestResults));
  }

  // اختبار البروكسيات في الخلفية
  setTimeout(testAllProxies, 2000);
});

// تهيئة عند تثبيت الإضافة
chrome.runtime.onInstalled.addListener(() => {
  console.log('تم تثبيت إضافة Fast US VPN');
  updateIcon(false);

  // اختبار البروكسيات عند التثبيت
  setTimeout(testAllProxies, 1000);
});
