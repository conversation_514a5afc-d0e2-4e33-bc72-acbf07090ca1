// إنشاء أيقونات PNG للإضافة

function createIcon(size, isActive = false) {
    const canvas = document.createElement('canvas');
    canvas.width = size;
    canvas.height = size;
    const ctx = canvas.getContext('2d');
    
    // خلفية دائرية
    const gradient = ctx.createLinearGradient(0, 0, size, size);
    if (isActive) {
        gradient.addColorStop(0, '#27ae60');
        gradient.addColorStop(1, '#2ecc71');
    } else {
        gradient.addColorStop(0, '#667eea');
        gradient.addColorStop(1, '#764ba2');
    }
    
    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(size/2, size/2, size/2 - 2, 0, 2 * Math.PI);
    ctx.fill();
    
    // إطار أبيض
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 2;
    ctx.stroke();
    
    // رسم درع
    const centerX = size / 2;
    const centerY = size / 2;
    const shieldWidth = size * 0.4;
    const shieldHeight = size * 0.5;
    
    ctx.fillStyle = isActive ? '#00b894' : '#f093fb';
    ctx.beginPath();
    ctx.moveTo(centerX, centerY - shieldHeight/2);
    ctx.lineTo(centerX - shieldWidth/2, centerY - shieldHeight/3);
    ctx.lineTo(centerX - shieldWidth/2, centerY + shieldHeight/6);
    ctx.quadraticCurveTo(centerX - shieldWidth/2, centerY + shieldHeight/2, centerX, centerY + shieldHeight/2);
    ctx.quadraticCurveTo(centerX + shieldWidth/2, centerY + shieldHeight/2, centerX + shieldWidth/2, centerY + shieldHeight/6);
    ctx.lineTo(centerX + shieldWidth/2, centerY - shieldHeight/3);
    ctx.closePath();
    ctx.fill();
    
    // رسم قفل
    const lockSize = size * 0.15;
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(centerX - lockSize/2, centerY - lockSize/4, lockSize, lockSize);
    
    // مقبض القفل
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = Math.max(1, size/32);
    ctx.beginPath();
    if (isActive) {
        // قفل مفتوح
        ctx.arc(centerX - lockSize/4, centerY - lockSize/2, lockSize/3, Math.PI, 0, false);
        ctx.lineTo(centerX + lockSize/6, centerY - lockSize/6);
    } else {
        // قفل مغلق
        ctx.arc(centerX, centerY - lockSize/2, lockSize/3, Math.PI, 0, false);
    }
    ctx.stroke();
    
    // علم أمريكي صغير
    const flagWidth = size * 0.25;
    const flagHeight = size * 0.15;
    const flagX = centerX - flagWidth/2;
    const flagY = centerY + shieldHeight/4;
    
    // خلفية بيضاء للعلم
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(flagX, flagY, flagWidth, flagHeight);
    
    // خطوط حمراء
    ctx.fillStyle = '#e74c3c';
    for (let i = 0; i < 4; i++) {
        ctx.fillRect(flagX, flagY + i * flagHeight/6, flagWidth, flagHeight/12);
    }
    
    // مربع أزرق
    ctx.fillStyle = '#3498db';
    ctx.fillRect(flagX, flagY, flagWidth/2.5, flagHeight/2);
    
    // نجوم بيضاء صغيرة
    ctx.fillStyle = '#ffffff';
    const starSize = Math.max(1, size/64);
    for (let i = 0; i < 3; i++) {
        for (let j = 0; j < 2; j++) {
            ctx.beginPath();
            ctx.arc(flagX + (i+1) * flagWidth/8, flagY + (j+1) * flagHeight/6, starSize, 0, 2 * Math.PI);
            ctx.fill();
        }
    }
    
    return canvas.toDataURL('image/png');
}

// إنشاء جميع الأحجام
const sizes = [16, 32, 48, 128];
const icons = {};

sizes.forEach(size => {
    icons[`icon${size}`] = createIcon(size, false);
    icons[`icon${size}_active`] = createIcon(size, true);
});

console.log('تم إنشاء الأيقونات:', icons);
