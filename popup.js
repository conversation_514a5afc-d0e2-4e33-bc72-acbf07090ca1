// منطق واجهة المستخدم لإضافة Fast US VPN

class VPNPopup {
    constructor() {
        this.isConnected = false;
        this.currentProxy = null;
        this.testResults = {};

        this.initializeElements();
        this.attachEventListeners();
        this.loadInitialState();
        this.populateServerList();
    }

    initializeElements() {
        this.toggleBtn = document.getElementById('toggleBtn');
        this.toggleIcon = document.getElementById('toggleIcon');
        this.toggleText = document.getElementById('toggleText');
        this.statusDot = document.getElementById('statusDot');
        this.statusText = document.getElementById('statusText');
        this.connectionInfo = document.getElementById('connectionInfo');
        this.proxyInfo = document.getElementById('proxyInfo');
        this.speedInfo = document.getElementById('speedInfo');
        this.serverList = document.getElementById('serverList');
        this.testBtn = document.getElementById('testBtn');
        this.switchBtn = document.getElementById('switchBtn');
        this.refreshBtn = document.getElementById('refreshBtn');
        this.loading = document.getElementById('loading');
    }

    attachEventListeners() {
        this.toggleBtn.addEventListener('click', () => this.toggleVPN());
        this.testBtn.addEventListener('click', () => this.testProxies());
        this.switchBtn.addEventListener('click', () => this.switchProxy());
        this.refreshBtn.addEventListener('click', () => this.refreshProxyList());
    }

    async loadInitialState() {
        try {
            const response = await chrome.runtime.sendMessage({ action: 'getStatus' });
            this.updateUI(response.isEnabled, response.currentProxy);
            this.testResults = response.testResults || {};
            this.updateServerList();
        } catch (error) {
            console.error('خطأ في تحميل الحالة الأولية:', error);
        }
    }

    populateServerList() {
        this.serverList.innerHTML = '';

        US_PROXIES.forEach((proxy, index) => {
            const serverItem = document.createElement('div');
            serverItem.className = 'server-item';
            serverItem.dataset.index = index;

            const key = `${proxy.host}:${proxy.port}`;
            const testResult = this.testResults[key];
            const latency = testResult ? testResult.latency : 'غير معروف';
            const speedClass = this.getSpeedClass(testResult);

            serverItem.innerHTML = `
                <div class="server-name">${proxy.location}</div>
                <div class="server-speed ${speedClass}">${this.formatLatency(latency)}</div>
            `;

            serverItem.addEventListener('click', () => this.selectServer(index));
            this.serverList.appendChild(serverItem);
        });
    }

    updateServerList() {
        const serverItems = this.serverList.querySelectorAll('.server-item');
        serverItems.forEach((item, index) => {
            const proxy = US_PROXIES[index];
            const key = `${proxy.host}:${proxy.port}`;
            const testResult = this.testResults[key];
            const speedElement = item.querySelector('.server-speed');

            if (speedElement) {
                const latency = testResult ? testResult.latency : 'غير معروف';
                const speedClass = this.getSpeedClass(testResult);
                speedElement.textContent = this.formatLatency(latency);
                speedElement.className = `server-speed ${speedClass}`;
            }

            // تمييز الخادم النشط
            if (this.currentProxy &&
                proxy.host === this.currentProxy.host &&
                proxy.port === this.currentProxy.port) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }

    getSpeedClass(testResult) {
        if (!testResult || !testResult.success) return 'speed-slow';
        if (testResult.latency < 200) return 'speed-fast';
        if (testResult.latency < 500) return 'speed-medium';
        return 'speed-slow';
    }

    formatLatency(latency) {
        if (latency === 'غير معروف' || latency === 9999) return 'غير معروف';
        return `${latency}ms`;
    }

    async toggleVPN() {
        this.showLoading(true);

        try {
            const response = await chrome.runtime.sendMessage({ action: 'toggleVpn' });

            if (response.success) {
                this.updateUI(response.enabled, response.proxy);
                this.showNotification(
                    response.enabled ? 'تم تفعيل VPN بنجاح!' : 'تم إلغاء تفعيل VPN',
                    'success'
                );
            } else {
                this.showNotification('فشل في تغيير حالة VPN', 'error');
            }
        } catch (error) {
            console.error('خطأ في تبديل VPN:', error);
            this.showNotification('حدث خطأ غير متوقع', 'error');
        }

        this.showLoading(false);
    }

    async testProxies() {
        this.showLoading(true);
        this.showNotification('جاري اختبار البروكسيات...', 'info');

        try {
            await chrome.runtime.sendMessage({ action: 'testProxies' });

            // انتظار قليل للحصول على النتائج المحدثة
            setTimeout(async () => {
                const response = await chrome.runtime.sendMessage({ action: 'getStatus' });
                this.testResults = response.testResults || {};
                this.updateServerList();
                this.showNotification('تم اختبار البروكسيات بنجاح!', 'success');
                this.showLoading(false);
            }, 3000);
        } catch (error) {
            console.error('خطأ في اختبار البروكسيات:', error);
            this.showNotification('فشل في اختبار البروكسيات', 'error');
            this.showLoading(false);
        }
    }

    async switchProxy() {
        if (!this.isConnected) {
            this.showNotification('يجب تفعيل VPN أولاً', 'warning');
            return;
        }

        this.showLoading(true);

        try {
            const response = await chrome.runtime.sendMessage({ action: 'switchProxy' });

            if (response.success) {
                this.updateUI(true, response.proxy);
                this.showNotification('تم تبديل الخادم بنجاح!', 'success');
            } else {
                this.showNotification('فشل في تبديل الخادم', 'error');
            }
        } catch (error) {
            console.error('خطأ في تبديل البروكسي:', error);
            this.showNotification('حدث خطأ في تبديل الخادم', 'error');
        }

        this.showLoading(false);
    }

    async selectServer(index) {
        const proxy = US_PROXIES[index];

        if (this.isConnected) {
            this.showLoading(true);

            try {
                // إعداد البروكسي المحدد
                const response = await chrome.runtime.sendMessage({
                    action: 'setSpecificProxy',
                    proxy: proxy
                });

                if (response && response.success) {
                    this.updateUI(true, proxy);
                    this.showNotification(`تم الاتصال بـ ${proxy.location}`, 'success');
                } else {
                    this.showNotification('فشل في الاتصال بالخادم المحدد', 'error');
                }
            } catch (error) {
                console.error('خطأ في اختيار الخادم:', error);
                this.showNotification('حدث خطأ في اختيار الخادم', 'error');
            }

            this.showLoading(false);
        } else {
            this.showNotification('يجب تفعيل VPN أولاً', 'warning');
        }
    }

    refreshProxyList() {
        this.populateServerList();
        this.testProxies();
    }

    updateUI(isConnected, proxy) {
        this.isConnected = isConnected;
        this.currentProxy = proxy;

        // تحديث زر التبديل
        if (isConnected) {
            this.toggleBtn.classList.add('active');
            this.toggleIcon.textContent = '🔓';
            this.toggleText.textContent = 'إلغاء VPN';
            this.statusDot.classList.add('connected');
            this.statusText.textContent = 'متصل';
            this.connectionInfo.style.display = 'block';

            if (proxy) {
                this.proxyInfo.textContent = `${proxy.host}:${proxy.port}`;
                const key = `${proxy.host}:${proxy.port}`;
                const testResult = this.testResults[key];
                this.speedInfo.textContent = testResult ?
                    this.formatLatency(testResult.latency) : 'غير معروف';
            }
        } else {
            this.toggleBtn.classList.remove('active');
            this.toggleIcon.textContent = '🔒';
            this.toggleText.textContent = 'تفعيل VPN';
            this.statusDot.classList.remove('connected');
            this.statusText.textContent = 'غير متصل';
            this.connectionInfo.style.display = 'none';
        }

        this.updateServerList();
    }

    showLoading(show) {
        this.loading.style.display = show ? 'block' : 'none';
    }

    showNotification(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);

        // إشعار مؤقت بسيط
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// تهيئة الواجهة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new VPNPopup();
});