// قائمة البروكسيات الافتراضية (يمكن تركها فارغة إذا كنت ستعتمد على المستخدم بالكامل)
let proxyList = [
  {
    name: "US Proxy 1 (Example)",
    host: "us-proxy1.example.com",
    port: 8080
  }
];

// تحميل قائمة البروكسيات عند فتح النافذة
document.addEventListener('DOMContentLoaded', async () => {
  const select = document.getElementById('proxyList');
  const connectBtn = document.getElementById('connect');
  const disconnectBtn = document.getElementById('disconnect');
  const statusDiv = document.getElementById('status');
  const proxyNameInput = document.getElementById('proxyName');
  const proxyHostInput = document.getElementById('proxyHost');
  const proxyPortInput = document.getElementById('proxyPort');
  const addProxyBtn = document.getElementById('addProxy');
  const removeProxyBtn = document.getElementById('removeProxy');

  // تحميل البروكسيات المحفوظة
  async function loadProxies() {
    const result = await chrome.storage.local.get(['customProxies']);
    const customProxies = result.customProxies || [];
    // دمج البروكسيات الافتراضية مع المخصصة (مع تجنب التكرار إذا كان الافتراضي موجود)
    proxyList = [...proxyList.filter(p => !customProxies.find(cp => cp.host === p.host && cp.port === p.port)), ...customProxies];
    renderProxyList();
  }

  // عرض قائمة البروكسيات في القائمة المنسدلة
  function renderProxyList() {
    select.innerHTML = '<option value="">اختر سيرفر بروكسي</option>'; // مسح القائمة القديمة
    proxyList.forEach(proxy => {
      const option = document.createElement('option');
      option.value = JSON.stringify(proxy);
      option.textContent = proxy.name;
      select.appendChild(option);
    });
  }

  await loadProxies();

  // زر الاتصال
  connectBtn.addEventListener('click', async () => {
    if (!select.value) {
        alert('الرجاء اختيار بروكسي');
        return;
    }
    const selectedProxy = JSON.parse(select.value);
    
    const config = {
      mode: "fixed_servers",
      rules: {
        singleProxy: {
          scheme: "http", // افترض http، يمكنك تعديلها إذا كانت البروكسيات تدعم https
          host: selectedProxy.host,
          port: parseInt(selectedProxy.port, 10)
        },
        bypassList: ["localhost"]
      }
    };

    chrome.runtime.sendMessage({ action: "setProxy", config }, (response) => {
      if (response && response.success) {
        statusDiv.textContent = `متصل بـ: ${selectedProxy.name}`;
        statusDiv.className = 'status connected';
        chrome.storage.local.set({ currentProxy: selectedProxy }); // حفظ البروكسي الحالي
      } else {
        statusDiv.textContent = 'فشل الاتصال بالبروكسي';
        statusDiv.className = 'status disconnected';
        alert(`فشل الاتصال بـ ${selectedProxy.name}. تأكد من أن البروكسي يعمل.`);
      }
    });
  });

  // زر قطع الاتصال
  disconnectBtn.addEventListener('click', () => {
    chrome.runtime.sendMessage({ action: "clearProxy" }, (response) => {
      if (response && response.success) {
        statusDiv.textContent = 'غير متصل';
        statusDiv.className = 'status disconnected';
        chrome.storage.local.remove('currentProxy');
      }
    });
  });

  // زر إضافة بروكسي جديد
  addProxyBtn.addEventListener('click', async () => {
    const name = proxyNameInput.value.trim();
    const host = proxyHostInput.value.trim();
    const port = parseInt(proxyPortInput.value.trim(), 10);

    if (!name || !host || !port) {
      alert('الرجاء ملء جميع حقول البروكسي.');
      return;
    }
    if (port <= 0 || port > 65535) {
        alert('رقم المنفذ غير صالح.');
        return;
    }

    const newProxy = { name, host, port };
    
    // التأكد من عدم وجود البروكسي مسبقاً
    if (proxyList.some(p => p.host === newProxy.host && p.port === newProxy.port)) {
        alert('هذا البروكسي موجود بالفعل.');
        return;
    }

    proxyList.push(newProxy);
    await chrome.storage.local.set({ customProxies: proxyList.filter(p => !proxyList.find(dp => dp.host === p.host && dp.port === p.port && dp.name.includes("(Example)"))) }); // حفظ البروكسيات المخصصة فقط
    renderProxyList();
    proxyNameInput.value = '';
    proxyHostInput.value = '';
    proxyPortInput.value = '';
    alert('تمت إضافة البروكسي بنجاح!');
  });

  // زر إزالة البروكسي المحدد
  removeProxyBtn.addEventListener('click', async () => {
    if (!select.value) {
        alert('الرجاء اختيار بروكسي لإزالته.');
        return;
    }
    const selectedProxyValue = select.value;
    const selectedProxy = JSON.parse(selectedProxyValue);

    proxyList = proxyList.filter(p => !(p.host === selectedProxy.host && p.port === selectedProxy.port));
    await chrome.storage.local.set({ customProxies: proxyList.filter(p => !proxyList.find(dp => dp.host === p.host && dp.port === p.port && dp.name.includes("(Example)"))) });
    renderProxyList();
    alert(`تمت إزالة البروكسي: ${selectedProxy.name}`);
  });

  // التحقق من حالة الاتصال عند تحميل النافذة
  chrome.storage.local.get(['currentProxy'], (result) => {
      if (result.currentProxy) {
        statusDiv.textContent = `متصل حاليًا بـ: ${result.currentProxy.name}`;
        statusDiv.className = 'status connected';
      } else {
        statusDiv.textContent = 'غير متصل';
        statusDiv.className = 'status disconnected';
      }
  });
}); 